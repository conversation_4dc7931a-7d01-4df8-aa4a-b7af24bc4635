<template>
  <div class="goods-choose-board">
    <div class="mask-board" @click="closeBoard"></div>
    <div class="goods-choose-content">
      <span class="close-btn" @click="closeBoard"/>
      <div class="goods-specification">
        <div class="header">
          <div :class="{ img: true, 'goods__sold-out': !stockState}">
            <img v-if="smallImg" :src="smallImg" alt="" data-name="goods-comp-goodschoose"/>
          </div>
          <div class="info">
            <p class="price">
              <span class="goods-price orange-text" v-if="realPrice">
                ￥<i>{{ realPrice[0] }}</i>.{{ realPrice[1] }}
              </span>
              <span class="del-price" v-if="originPrice">￥{{ originPrice }}</span>
            </p>
            <p class="choose-result">已选 {{ paramString }}</p>
          </div>
        </div>
        <div class="line"/>

        <div class="specification-wrapper">
          <div class="specification" v-if="isShowSpecs">
            <div class="option-classification">
              <h3 class="title">选择规格</h3>
              <h3 class="sub-title"
                  v-clipboard:copy="currSku.supplierSkuId"
                  v-clipboard:success="onClipboardCopy"
                  v-clipboard:error="onClipboardError"
                  v-if="isShowProductCodeSpecs">商品编号：<span class="underline">{{currSku.supplierSkuId}}</span></h3>
            </div>
            <div class="spec-content">
              <div class="radio-wrapper" v-for="(specs,groupIndex) in specsList" :key="groupIndex">
                <button
                  v-for="(spec , specIndex) in specs"
                  :key="specIndex"
                  :class="{active:curSpecs.indexOf(spec)>=0,disabled:curDisabledSpecs.indexOf(spec)>=0}"
                  @click="selectSpec(spec)"
                >
                  {{ removeSpecPrefix(spec) }}
                </button>
                <div class="specs-button-division-line" v-if="specs.length > 0"></div>
              </div>
            </div>
          </div>
          <div class="supplier-info-wrapper">
            <div class="sub-title sub-title-padding"
                 v-clipboard:copy="currSku.supplierSkuId"
                 v-clipboard:success="onClipboardCopy"
                 v-clipboard:error="onClipboardError"
                 v-if="isShowProductCodeNoSpecs">商品编号：<span class="underline">{{currSku.supplierSkuId}}</span></div>
          </div>
          <div class="pay-method-wrapper" v-if="loanProductList && loanProductList.length>0">
            <pay-method
                :loanProductList="loanProductList"
                :amount="realPrice"/>
          </div>
          <div class="counter-wrapper">
            <h3 class="title">购买数量<span style="color: #f00;" v-if="xgObj.isXg && xgObj.limitText">（{{ xgObj.limitText }}）</span><span style="color: #f00;" v-else-if="lowestBuyObj.isLowestBuy" >（{{lowestBuyObj.lowestBuyText}}）</span></h3>
            <div class="counter">
              <span class="minus" :class="reduceGoodsNumDisabled" @click="reduceGoodsNum">-</span>
              <input
                type="number"
                v-model="goodsNum"
                @input="setGoodsNum($event)"
                @blur="leftBlur"
                :disabled="inputNumDisabled"/>
              <span
                class="plus"
                :class="addGoodsNumDisabled"
                @click="addGoodsNum">
                +
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="bottomBtn">
      <div v-if="popType!=='1'" class="confirm-btn" @click="submitGoods(0)">确定</div>
      <div
        v-if="popType==='1'"
        class="add-btn"
        :class="isSale && !detailErr ? '' : 'disabled'"
        @click="isSale && !detailErr ? submitGoods(1) : ''">
        加入购物车
      </div>
      <div
        v-if="popType==='1'"
        class="buy-btn"
        :class="isSale && !detailErr ? '' : 'disabled'"
        @click="isSale && !detailErr ? submitGoods(2) : ''">
        立即购买
      </div>
    </div>
  </div>
</template>

<script>
import PayMethod from '@/components/PayMethod/Index.vue'
import { fenToYuan, priceComputeFromSku, splitAmt } from '@/utils/amount'
import { removeSpecPrefix } from '@/utils/goodsDetail'

export default {
  components: { PayMethod },
  props: {
    // 当前sku的商品信息
    currSku: Object,
    // 当前规格所有信息
    specsList: {
      type: Array,
      default: () => {
        return []
      }
    },
    curSpecs: Array, // 当前选中的规格
    curDisabledSpecs: Array, // 当前禁止选中的规格
    loanProductList: Array, // 借款商品信息
    popType: String, // 1.点击规格 2.购物车 3.立即购买
    // 是否有货（需要单独配置，没有在内部处理）
    stockState: {
      type: Boolean,
      default: true
    },
    goodsMultiState: {
      type: Boolean,
      default: true
    },
    xgObj: {
      type: Object,
      default: () => {}
    },
    lowestBuyObj: {
      type: Object,
      default: () => {}
    },
    detailErr: {
      type: Boolean,
      default: false
    },
    currSkuNum: {
      type: Number,
      default: 1
    }
  },
  data () {
    return {
      goodsNum: this.currSkuNum,
      isSale: true
    }
  },
  computed: {
    isShowSpecs () {
      return this.specsList.length > 0 && this.specsList[0].length > 0
    },
    isShowProductCodeNoSpecs () {
      return this.currSku.supplierSkuId && this.currSku.isSpecsComplete &&
        (
          (this.specsList.length === 0) ||
          (this.specsList.length > 0 && this.specsList[0].length === 0)
        )
    },
    isShowProductCodeSpecs () {
      return this.currSku.supplierSkuId && this.currSku.isSpecsComplete
    },
    inputNumDisabled () {
      if (this.xgObj.isXg && this.xgObj.limitText) {
        return this.goodsNum >= this.xgObj.limitNum
      }
      return !this.isSale
    },
    reduceGoodsNumDisabled () {
      if (this.lowestBuyObj.isLowestBuy) {
        if (this.goodsNum <= this.lowestBuyObj.lowestBuyNum) {
          return 'disabled'
        } else {
          return ''
        }
      } else {
        if (this.goodsNum <= 1) {
          return 'disabled'
        } else {
          return ''
        }
      }
    },
    addGoodsNumDisabled () {
      if (this.xgObj.isXg && this.xgObj.limitText) {
        if (this.goodsNum >= this.xgObj.limitNum) {
          return 'disabled'
        } else {
          return ''
        }
      } else if (parseInt(this.currSku.stock) <= this.goodsNum) {
        return 'disabled'
      } else {
        return ''
      }
    },
    smallImg () {
      return this.currSku && this.currSku.detailImageUrl ? this.currSku.detailImageUrl[0].split(',')[0] : ''
    },
    realPrice () {
      const amt = priceComputeFromSku(this.currSku)[0]
      return splitAmt(amt)
    },
    originPrice () {
      const amt = priceComputeFromSku(this.currSku)[1]
      return amt ? fenToYuan(amt) : ''
    },
    paramString () {
      if (this.curSpecs && this.curSpecs.length > 0) {
        return this.curSpecs.map(item => item.substring(4)).join(' , ')
      } else {
        return ''
      }
    }
  },
  watch: {
    goodsMultiState: {
      handler (val, oldVal) {
        this.isSale = val
      },
      immediate: true
    },
    // 当前切换数据
    currSku: {
      handler (newVal, oldVal) {
        if (JSON.stringify(newVal) !== JSON.stringify(oldVal) && this.lowestBuyObj.isLowestBuy) {
          if (this.currSkuNum >= +this.lowestBuyObj.lowestBuyNum) {
            this.goodsNum = +this.currSkuNum
          } else {
            this.goodsNum = +this.lowestBuyObj.lowestBuyNum
          }

          this.$emit('chooseFinish', '', this.goodsNum)
        } else if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
          this.goodsNum = this.currSkuNum
          this.$emit('chooseFinish', '', this.goodsNum)
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    removeSpecPrefix,
    // 剪切板复制
    onClipboardCopy () {
      this.$toast('复制成功')
      // this.$toast({
      //   message: '复制成功',
      //   className: 'copyClass'
      // })
    },
    // 剪切板错误
    onClipboardError (e) {
      this.$toast('复制失败')
    },
    selectSpec (spec) {
      if (this.curSpecs.indexOf(spec) < 0) {
        this.goodsNum = 1
      }
      this.$emit('chooseFinish', spec, this.goodsNum)
    },
    reduceGoodsNum () {
      if (this.xgObj.isXg && this.xgObj.limitText) {
        if (this.goodsNum > 1) {
          this.goodsNum--
          this.$emit('chooseFinish', '', this.goodsNum)
        } else {
          this.$toast('最少购买1件哦')
        }
      } else if (this.lowestBuyObj.isLowestBuy) {
        if (this.goodsNum <= this.lowestBuyObj.lowestBuyNum) {
          this.$toast('最少购买' + this.lowestBuyObj.lowestBuyNum + '件哦')
        } else {
          this.goodsNum--
          this.$emit('chooseFinish', '', this.goodsNum)
        }
      } else {
        if (this.goodsNum > 1) {
          this.goodsNum--
          this.$emit('chooseFinish', '', this.goodsNum)
        } else {
          this.$toast('最少购买1件哦')
        }
      }
    },
    addGoodsNum () {
      const stockNum = parseInt(this.currSku.stock)
      if (this.xgObj.isXg && this.xgObj.limitText) {
        if (this.goodsNum >= this.xgObj.limitNum) {
          this.$toast('最多购买' + this.xgObj.limitNum + '件哦')
          return
        }
      } else if (this.goodsNum >= stockNum) {
        this.$toast('最多购买' + stockNum + '件哦')
        return
      }
      this.goodsNum++
      this.$emit('chooseFinish', '', this.goodsNum)
    },
    closeBoard () {
      this.$emit('closeBoard')
    },
    // 处理输入框填写商品数量
    setGoodsNum (event) {
      const currNumber = Math.abs(+event.currentTarget.value)
      // TODO: 这个地方需要注意用户输入的数字的类型的和文本等
      const stockNum = parseInt(this.currSku.stock)
      // 限购策略 优先级1
      if (this.xgObj.isXg && this.xgObj.limitText) {
        if (this.goodsNum > this.xgObj.limitNum) {
          this.$toast('最多购买' + this.xgObj.limitNum + '件哦')
          this.goodsNum = currNumber > this.xgObj.limitNum ? this.xgObj.limitNum : currNumber
        }
      } else if (currNumber > stockNum) {
        this.$toast('最多购买' + stockNum + '件哦')
        this.goodsNum = currNumber > stockNum ? stockNum : currNumber
      }
      this.$emit('chooseFinish', '', this.goodsNum)
    },
    leftBlur () {
      // 起购数量 优先级2
      // 失去焦点的时候开始 校验
      if (this.lowestBuyObj.isLowestBuy && this.goodsNum < this.lowestBuyObj.lowestBuyNum) {
        this.$toast('最少购买' + this.lowestBuyObj.lowestBuyNum + '件哦')
        this.goodsNum = this.goodsNum < this.lowestBuyObj.lowestBuyNum ? this.lowestBuyObj.lowestBuyNum : this.goodsNum
        this.$emit('chooseFinish', '', this.goodsNum)
      } else {
        this.goodsNum = +this.goodsNum === 0 ? 1 : +this.goodsNum
        this.$emit('chooseFinish', '', this.goodsNum)
      }
    },
    // 完成选择规格
    submitGoods (type) {
      if (type === 1 || type === 2) {
        if (!this.isSale) return
      }
      // type 0:确定按钮  1：加入购物车  2：立即购买
      this.$emit('clickSubmit', type, this.goodsNum)
      if (this.lowestBuyObj.isLowestBuy) {
        this.goodsNum = +this.lowestBuyObj.lowestBuyNum
      } else {
        this.goodsNum = 1
      }
    }
  }
}
</script>
<style lang=less>
.copyClass {
  color: #000000 !important;
  background-color: transparent !important;
}
</style>
<style scoped lang=less>
@screen-small: 320px;
@screen-medium: 540px;
.goods-choose-board {
  position: fixed;
  width: 100%;
  height: 100%;
  left: 0;
  bottom: 0;
  z-index: 1000;
}

.mask-board {
  width: 100%;
  height: 100%;
  background: #000;
  opacity: .4;
  position: absolute;
  left: 0;
  top: 0;
}

.goods-choose-content {
  width: 100%;
  height: 75%;
  background: #fff;
  position: absolute;
  left: 0;
  bottom: 0;
  border-radius: 16px 16px 0 0;
  overflow: hidden;
}

.bottomBtn {
  display: flex;
  width: 750px;
  height: 98px;
  line-height: 98px;
  background-color: white;
  position: fixed;
  left: 0;
  bottom: 30px;
  @media (min-width: @screen-medium + 1) {
    width: 100%;
    height: 40Px;
    justify-content: space-around;
    align-items: center;
  }
  .confirm-btn {
    width: 682px;
    height: 72px;
    line-height: 72px;
    background-image: linear-gradient(135deg, #FFA033 0%, #FF6D33 100%);
    font-size: 32px;
    color: #FFFFFF;
    text-align: center;
    margin-left: 34px;
    margin-top: 13px;
    border-radius: 36px;
    @media (min-width: @screen-medium + 1) {
      margin-left: 0;
      margin-top: 0;
      white-space: nowrap;
      outline: none;
      border: none;
      height: 36Px;
      line-height: 36Px;
      width: 350Px;
      font-size: 14Px;
      color: #fff;
      border-radius: 18Px;
    }
  }

  .confirm-btn.disabled {
    opacity: .5;
  }

  .add-btn {
    width: 332px;
    height: 72px;
    line-height: 72px;
    background-image: linear-gradient(146deg, #FFD72D 0%, #FFAD1B 87%);
    border-radius: 35px;
    font-size: 28px;
    color: #FFFFFF;
    text-align: center;
    margin-left: 34px;
    margin-top: 13px;

    @media (min-width: @screen-medium + 1) {
      margin-left: 0;
      margin-top: 0;
      white-space: nowrap;
      outline: none;
      border: none;
      height: 36Px;
      line-height: 36Px;
      width: 280px;
      font-size: 14Px;
      color: #fff;
      border-radius: 18Px;
    }
  }

  .add-btn.disabled {
    opacity: .5;
  }

  .buy-btn {
    font-size: 28px;
    color: #FFFFFF;
    width: 332px;
    height: 72px;
    line-height: 72px;
    background-image: linear-gradient(121deg, #FFA033 0%, #FF6D33 68%);
    border-radius: 36px;
    text-align: center;
    margin-left: 18px;
    margin-top: 13px;

    @media (min-width: @screen-medium + 1) {
      margin-left: 0;
      margin-top: 0;
      white-space: nowrap;
      outline: none;
      border: none;
      height: 36Px;
      line-height: 36Px;
      width: 280px;
      font-size: 14Px;
      color: #fff;
      border-radius: 18Px;
    }
  }

  .buy-btn.disabled {
    opacity: .5;
  }
}

.close-btn {
  display: block;
  width: 40px;
  height: 40px;
  background: url(../assets/close-btn.png) no-repeat 0 0;
  background-size: 100%;
  position: absolute;
  right: 34px;
  top: 34px;
  @media (min-width: @screen-medium + 1) {
    width: 25Px;
    height: 25Px;
  }
}

.goods-specification {
  width: 100%;
  height: 100%;
  padding: 40px 34px 0;
  @media (min-width: @screen-medium + 1) {
    padding: 30Px 30Px 0;
  }
}

.goods-specification .header {
  width: 100%;
  display: flex;
  align-items: center;
}

.goods-specification .header .img {
  display: block;
  width: 174px;
  height: 174px;
  background: #f7f7f7;
  border-radius: 8px;
  border: 1px solid #f7f7f7;
  overflow: hidden;
  @media (min-width: @screen-medium + 1) {
    width: 150Px;
    height: 150Px;
  }
  img {
    width: 100%;
  }
}

.goods-specification .header .info {
  margin-left: 50px;
  padding-top: 72px;
  @media (min-width: @screen-medium + 1) {
    margin-left: 20Px;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    padding-top: 0;
    height: 150Px;
  }
}

.price .goods-price {
  font-size: 24Px;
}

.price .del-price {
  margin-left: 12Px;
  font-size: 13Px;
  color: #879099;
  text-decoration: line-through;
}

.choose-result {
  font-size: 24px;
  color: #171E24;
  margin-top: 10px;
  @media (min-width: @screen-medium + 1) {
    margin-top: 10Px;
    font-size: 18Px;
  }
}

.line {
  width: 100%;
  height: 1px;
  background: #f7f7f7;
  margin-top: 40px;
  @media (min-width: @screen-medium + 1) {
    margin-top: 10Px;
  }
}

.specification-wrapper {
  width: 100%;
  height: calc(100% - 315px);
  overflow: scroll;
  @media (min-width: @screen-medium + 1) {
    overflow: hidden;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: calc(100% - 250Px);
  }
  .sub-title {
    font-size: 24px;
    color: #999999;
    line-height: 36px;
    text-align: right;
    .underline {
      text-decoration: underline;
    }
    @media (min-width: @screen-medium + 1) {
      font-size: 16Px;
      line-height: 1.5;
    }
  }
  .sub-title-padding {
    padding: 30px 0 10px 0;
    @media (min-width: @screen-medium + 1) {
      padding: 15Px 0 10Px 0;
    }
  }
}

.specification {
  padding: 30px 0 10px 0;
  @media (min-width: @screen-medium + 1) {
    height: calc(100% - 45Px);
    display: flex;
    flex-direction: column;
    padding: 15Px 0 10Px 0;
  }
  .option-classification {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;

    @media (min-width: @screen-medium + 1) {
      margin-bottom: 10Px;
    }
  }
}

.specification:last-child {
  margin-bottom: 30px;
}

.title {
  font-size: 24px;
  color: #171E24;
  line-height: 36px;
  @media (min-width: @screen-medium + 1) {
    font-size: 16Px;
    line-height: 1.5;
  }
}
.spec-content {
  flex: 1;
  overflow-y: scroll;
}
.radio-wrapper {
  line-height: 40px;
  @media (min-width: @screen-medium + 1) {
    line-height: 0;
  }
  .specs-button-division-line {
    width: 100%;
    height: 1Px;
    background: rgba(229,229,229,0.64);
  }
  &:last-child .specs-button-division-line{
    height: 0;
  }
}

.counter-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 194px;
  margin-top: 30px;
  @media (min-width: @screen-medium + 1) {
    margin-top: 10Px;
    margin-bottom: 0;
  }
}

.counter-wrapper .title {
  margin-bottom: 0;
}

.counter {
  display: flex;
  align-items: center;
}

.counter span {
  width: 48px;
  height: 36px;
  line-height: 36px;
  text-align: center;
  font-size: 36px;
  @media (min-width: @screen-medium + 1) {
    font-size: 22Px;
  }
}

.counter span.minus {
  margin-right: 10px;
}

.counter span.plus {
  margin-left: 10px;
}

.counter span.disabled {
  color: #B1BEC9;
}

.counter input {
  display: block;
  background: #F8F8F8;
  border-radius: 2px;
  width: 68px;
  height: 36px;
  line-height: 36px;
  font-size: 26px;
  color: #171E24;
  text-align: center;
  @media (min-width: @screen-medium + 1) {
    font-size: 16Px;
  }
}

.counter input[disabled] {
  color: #171E24;
  opacity: 1
}

.radio-wrapper button {
  display: inline-block;
  min-width: 92px;
  font-size: 24px;
  color: #171E24;
  line-height: 24px;
  background: #F7F7F7;
  border-radius: 100px;
  padding: 15px 22px;
  margin-right: 20px;
  margin-bottom: 20px;
  outline: none;
  border: 1px solid transparent;
  @media (min-width: @screen-medium + 1) {
    margin-right: 10Px;
    margin-bottom: 10Px;
    padding: 3Px 10Px;
    font-size: 14Px;
    line-height: 1.5;
  }
}

.radio-wrapper button.active {
  background: rgba(255, 120, 10, 0.10);
  border: 1px solid #FF780A;
  border-radius: 100px;
  color: #FF780A;
}

.radio-wrapper button.disabled {
  background: #F7F7F7;
  /* border-radius: 20px 20px 1.5px 1.5px; */
  color: #B1BEC9;
}

.radio-wrapper button.grey {
  color: #ccc;
}

.pay-method-wrapper {
  padding: 30px 0;
}

</style>
